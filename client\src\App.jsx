import React from "react";
import { BrowserRouter, Route, Routes, Navigate } from "react-router-dom";
import AuthLayout from "./pages/AuthLayout";
import EmailVerificationSuccess from "./pages/EmailVerificationSuccess";
import VerifyEmail from "./pages/VerifyEmail";
import Dashboard from "./pages/Dashboard";
import ProtectedRoute from "./components/ProtectedRoute";
import { AuthProvider } from "./context/AuthContext";

const App = () => {
  return (
    <BrowserRouter>
      <AuthProvider>
        <Routes>
          <Route path="/" element={<Navigate to="/dashboard" replace />} />
          <Route path="/register" element={<AuthLayout />} />
          <Route
            path="/email-verification-success"
            element={<EmailVerificationSuccess />}
          />
          <Route
            path="/verify-email/:userId/:token"
            element={<VerifyEmail />}
          />
          <Route path="/login" element={<AuthLayout />} />
          <Route
            path="/dashboard"
            element={
              <ProtectedRoute>
                <Dashboard />
              </ProtectedRoute>
            }
          />
          <Route path="/forgot-password" element={<AuthLayout />} />
          <Route
            path="/reset-password/:userId/:token"
            element={<AuthLayout />}
          />
        </Routes>
      </AuthProvider>
    </BrowserRouter>
  );
};

export default App;
